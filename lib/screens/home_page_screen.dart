import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';

class HomePageScreen extends StatefulWidget {
  final bool showWelcomeMessage;

  const HomePageScreen({
    super.key,
    this.showWelcomeMessage = false,
  });

  @override
  State<HomePageScreen> createState() => _HomePageScreenState();
}

class _HomePageScreenState extends State<HomePageScreen> {
  bool _showWelcome = false;
  bool _isFirstTimeUser = false;

  @override
  void initState() {
    super.initState();
    if (widget.showWelcomeMessage) {
      _initializeWelcomeMessage();
    }
  }

  Future<void> _initializeWelcomeMessage() async {
    // Check if user is first time by looking at Firestore creation date
    final user = AuthService.currentUser;
    if (user?.uid != null) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user!.uid)
            .get();

        if (userDoc.exists) {
          final data = userDoc.data();
          final createdAt = data?['createdAt'] as Timestamp?;
          final lastSignIn = data?['lastSignIn'] as Timestamp?;

          // Consider user as first time if account was created in the last 5 minutes
          if (createdAt != null && lastSignIn != null) {
            final now = DateTime.now();
            final createdTime = createdAt.toDate();
            final timeDifference = now.difference(createdTime).inMinutes;

            setState(() {
              _isFirstTimeUser = timeDifference <= 5;
              _showWelcome = true;
            });

            // Hide welcome message after 4 seconds
            Future.delayed(const Duration(seconds: 4), () {
              if (mounted) {
                setState(() {
                  _showWelcome = false;
                });
              }
            });
          }
        }
      } catch (e) {
        // If there's an error, default to showing welcome back
        setState(() {
          _isFirstTimeUser = false;
          _showWelcome = true;
        });

        Future.delayed(const Duration(seconds: 4), () {
          if (mounted) {
            setState(() {
              _showWelcome = false;
            });
          }
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.currentUser;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        title: const Text(
          'EvenOut',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section (conditional)
            if (_showWelcome) ...[
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: const Color(0xFFA8C5B8), // Same sage green
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isFirstTimeUser ? 'Welcome!' : 'Welcome back!',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      user?.displayName ?? user?.email ?? 'User',
                      style: const TextStyle(
                        fontSize: 18,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _isFirstTimeUser
                          ? 'Welcome to EvenOut! Ready to start exchanging goods and connecting with your community?'
                          : 'Ready to exchange goods and connect with your community?',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF5A8A8A),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
            ],

            // Recent Activity or Featured Items
            const Text(
              'Recent Activity',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D5A5A),
              ),
            ),

            const SizedBox(height: 16),

            // Placeholder content for home page
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.home,
                        size: 64,
                        color: Color(0xFF5A8A8A),
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Welcome to EvenOut',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D5A5A),
                        ),
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Start exploring items in your community!',
                        style: TextStyle(
                          fontSize: 16,
                          color: Color(0xFF5A8A8A),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
