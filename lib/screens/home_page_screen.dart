import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'dart:math';
import '../services/auth_service.dart';

class HomePageScreen extends StatefulWidget {
  final bool showWelcomeMessage;

  const HomePageScreen({super.key, this.showWelcomeMessage = false});

  @override
  State<HomePageScreen> createState() => _HomePageScreenState();
}

class _HomePageScreenState extends State<HomePageScreen> {
  bool _showWelcome = false;
  bool _isFirstTimeUser = false;
  double? _currentLatitude;
  double? _currentLongitude;
  String? _currentCity;
  bool _isLocationLoading = true;
  List<Map<String, dynamic>> _sortedItems = [];

  @override
  void initState() {
    super.initState();
    if (widget.showWelcomeMessage) {
      _initializeWelcomeMessage();
    }
    _getCurrentLocationAndFetchItems();
  }

  Future<void> _initializeWelcomeMessage() async {
    // Check if user is first time by looking at Firestore creation date
    final user = AuthService.currentUser;
    if (user?.uid != null) {
      try {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user!.uid)
                .get();

        if (userDoc.exists) {
          final data = userDoc.data();
          final createdAt = data?['createdAt'] as Timestamp?;
          final lastSignIn = data?['lastSignIn'] as Timestamp?;

          // Consider user as first time if account was created in the last 5 minutes
          if (createdAt != null && lastSignIn != null) {
            final now = DateTime.now();
            final createdTime = createdAt.toDate();
            final timeDifference = now.difference(createdTime).inMinutes;

            setState(() {
              _isFirstTimeUser = timeDifference <= 5;
              _showWelcome = true;
            });

            // Hide welcome message after 4 seconds
            Future.delayed(const Duration(seconds: 4), () {
              if (mounted) {
                setState(() {
                  _showWelcome = false;
                });
              }
            });
          }
        }
      } catch (e) {
        // If there's an error, default to showing welcome back
        setState(() {
          _isFirstTimeUser = false;
          _showWelcome = true;
        });

        Future.delayed(const Duration(seconds: 4), () {
          if (mounted) {
            setState(() {
              _showWelcome = false;
            });
          }
        });
      }
    }
  }

  Future<void> _getCurrentLocationAndFetchItems() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() => _isLocationLoading = false);
        _fetchItemsWithoutLocation();
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          setState(() => _isLocationLoading = false);
          _fetchItemsWithoutLocation();
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        setState(() => _isLocationLoading = false);
        _fetchItemsWithoutLocation();
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      String city = '';
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        city =
            place.locality ??
            place.administrativeArea ??
            place.subAdministrativeArea ??
            '';
      }

      setState(() {
        _currentLatitude = position.latitude;
        _currentLongitude = position.longitude;
        _currentCity = city;
      });

      await _fetchAndSortItems();
    } catch (e) {
      setState(() => _isLocationLoading = false);
      _fetchItemsWithoutLocation();
    }
  }

  Future<void> _fetchAndSortItems() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) {
        setState(() => _isLocationLoading = false);
        return;
      }

      // Fetch all active items except user's own items
      final querySnapshot =
          await FirebaseFirestore.instance
              .collection('items')
              .where('isActive', isEqualTo: true)
              .where('userId', isNotEqualTo: user.uid)
              .get();

      List<Map<String, dynamic>> items = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        final itemLatitude = data['latitude'] as double?;
        final itemLongitude = data['longitude'] as double?;

        // Calculate distance if both user and item have coordinates
        double distance = double.infinity;
        if (_currentLatitude != null &&
            _currentLongitude != null &&
            itemLatitude != null &&
            itemLongitude != null) {
          distance = _calculateDistance(
            _currentLatitude!,
            _currentLongitude!,
            itemLatitude,
            itemLongitude,
          );
        }

        // Add distance to item data
        data['distance'] = distance;
        data['docId'] = doc.id;
        items.add(data);
      }

      // Sort items by distance (closest first)
      items.sort(
        (a, b) => (a['distance'] as double).compareTo(b['distance'] as double),
      );

      setState(() {
        _sortedItems = items;
        _isLocationLoading = false;
      });
    } catch (e) {
      setState(() => _isLocationLoading = false);
    }
  }

  Future<void> _fetchItemsWithoutLocation() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return;

      // Fetch all active items except user's own items
      final querySnapshot =
          await FirebaseFirestore.instance
              .collection('items')
              .where('isActive', isEqualTo: true)
              .where('userId', isNotEqualTo: user.uid)
              .orderBy('createdAt', descending: true)
              .get();

      List<Map<String, dynamic>> items = [];

      for (var doc in querySnapshot.docs) {
        final data = doc.data();
        data['distance'] = double.infinity;
        data['docId'] = doc.id;
        items.add(data);
      }

      setState(() {
        _sortedItems = items;
      });
    } catch (e) {
      // Handle error silently
    }
  }

  double _calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a =
        sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  String _formatDistance(double distance) {
    if (distance == double.infinity) {
      return 'Location unknown';
    } else if (distance < 1) {
      return '${(distance * 1000).round()}m away';
    } else {
      return '${distance.toStringAsFixed(1)}km away';
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = AuthService.currentUser;

    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        title: const Text(
          'EvenOut',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        actions: [
          if (_currentCity != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: const Color(0xFF3AD29F).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Color(0xFF3AD29F),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _currentCity!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF3AD29F),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
        ],
        actionsPadding: const EdgeInsets.only(right: 12),
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome section (conditional)
            if (_showWelcome) ...[
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: const Color(0xFFA8C5B8),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isFirstTimeUser ? 'Welcome!' : 'Welcome back!',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      user?.displayName ?? user?.email ?? 'User',
                      style: const TextStyle(
                        fontSize: 18,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _isFirstTimeUser
                          ? 'Welcome to EvenOut! Ready to start exchanging goods and connecting with your community?'
                          : 'Ready to exchange goods and connect with your community?',
                      style: const TextStyle(
                        fontSize: 16,
                        color: Color(0xFF5A8A8A),
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
            ],

            // Items list
            Expanded(
              child:
                  _isLocationLoading
                      ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(color: Color(0xFF3AD29F)),
                            SizedBox(height: 16),
                            Text(
                              'Finding items near you...',
                              style: TextStyle(
                                fontSize: 16,
                                color: Color(0xFF5A8A8A),
                              ),
                            ),
                          ],
                        ),
                      )
                      : _sortedItems.isEmpty
                      ? Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.05),
                              blurRadius: 10,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.inventory_2_outlined,
                                size: 64,
                                color: Color(0xFF5A8A8A),
                              ),
                              SizedBox(height: 16),
                              Text(
                                'No Items Available',
                                style: TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2D5A5A),
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                'Be the first to list an item in your area!',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF5A8A8A),
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      )
                      : ListView.builder(
                        itemCount: _sortedItems.length,
                        itemBuilder: (context, index) {
                          final item = _sortedItems[index];
                          return _buildItemCard(item);
                        },
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(Map<String, dynamic> item) {
    final images = item['images'] as List<dynamic>? ?? [];
    final imageUrl = images.isNotEmpty ? images[0] as String : null;
    final distance = item['distance'] as double;
    final price = item['price'] as double?;
    final swapOption = item['swapOption'] as String?;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item Image
          Container(
            height: 200,
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
              color: const Color(0xFFF8F8F8),
            ),
            child:
                imageUrl != null
                    ? ClipRRect(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(16),
                      ),
                      child: Image.network(
                        imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.image_not_supported,
                              size: 48,
                              color: Color(0xFF5A8A8A),
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return const Center(
                            child: CircularProgressIndicator(
                              color: Color(0xFF3AD29F),
                            ),
                          );
                        },
                      ),
                    )
                    : const Center(
                      child: Icon(
                        Icons.image,
                        size: 48,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
          ),

          // Item Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Item Name and Distance
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item['itemName'] ?? 'Unknown Item',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D5A5A),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color:
                            distance <= 20
                                ? const Color(0xFF3AD29F).withValues(alpha: 0.1)
                                : const Color(
                                  0xFFE6C068,
                                ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _formatDistance(distance),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color:
                              distance <= 20
                                  ? const Color(0xFF3AD29F)
                                  : const Color(0xFFE6C068),
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 8),

                // // Description
                // if (item['description'] != null &&
                //     item['description'].toString().isNotEmpty)
                //   Text(
                //     item['description'],
                //     style: const TextStyle(
                //       fontSize: 14,
                //       color: Color(0xFF5A8A8A),
                //       height: 1.4,
                //     ),
                //     maxLines: 2,
                //     overflow: TextOverflow.ellipsis,
                //   ),

                // const SizedBox(height: 12),

                // Price, Category, and Swap Option
                Row(
                  children: [
                    // Price
                    if (price != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF2D5A5A).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          '₹${price.toStringAsFixed(0)}',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2D5A5A),
                          ),
                        ),
                      ),

                    if (price != null) const SizedBox(width: 8),

                    // Category
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF8B5A8C).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        item['category'] ?? 'Other',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF8B5A8C),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Swap Option
                    if (swapOption != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color:
                              swapOption == 'Temporary Swap'
                                  ? const Color(
                                    0xFFE74C3C,
                                  ).withValues(alpha: 0.1)
                                  : const Color(
                                    0xFF3AD29F,
                                  ).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          swapOption == 'Temporary Swap' ? 'Temp' : 'Perm',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color:
                                swapOption == 'Temporary Swap'
                                    ? const Color(0xFFE74C3C)
                                    : const Color(0xFF3AD29F),
                          ),
                        ),
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // Location and User Info
                Row(
                  children: [
                    const Icon(
                      Icons.location_on,
                      size: 16,
                      color: Color(0xFF5A8A8A),
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        item['city'] ?? 'Location unknown',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF5A8A8A),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'by ${item['userName'] ?? 'Unknown User'}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF5A8A8A),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
