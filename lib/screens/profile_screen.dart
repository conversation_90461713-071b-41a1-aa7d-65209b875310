import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';
import 'my_items_screen.dart';
import 'profile_verification_screen.dart';
import 'edit_profile_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  int _profileUpdateCounter = 0;

  void _onProfileUpdated() {
    setState(() {
      _profileUpdateCounter++;
    });
  }

  Future<void> _handleSignOut(BuildContext context) async {
    try {
      await AuthService.signOut();
      if (mounted && context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
      }
    } catch (e) {
      if (mounted && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error signing out: ${e.toString()}'),
            backgroundColor: const Color(0xFFE74C3C),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<Map<String, dynamic>> _getUserVerificationData() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) {
        return {
          'emailVerified': false,
          'phoneVerified': false,
          'documentVerified': false,
          'verificationCount': 0,
        };
      }

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        final emailVerified = data['emailVerified'] ?? false;
        final phoneVerified = data['phoneVerified'] ?? false;
        final documentVerified = data['documentVerified'];

        // Handle document verification status ('true', 'false', or 'pending')
        bool isDocumentVerified = documentVerified == 'true';

        int verificationCount = 0;
        if (emailVerified) verificationCount++;
        if (phoneVerified) verificationCount++;
        if (isDocumentVerified) verificationCount++;

        return {
          'emailVerified': emailVerified,
          'phoneVerified': phoneVerified,
          'documentVerified': isDocumentVerified,
          'documentStatus': documentVerified, // Keep original status for detailed display
          'verificationCount': verificationCount,
          'userData': data,
        };
      }
    } catch (e) {
      debugPrint('Error fetching verification data: $e');
    }

    return {
      'emailVerified': false,
      'phoneVerified': false,
      'documentVerified': false,
      'verificationCount': 0,
    };
  }

  Future<Widget> _buildVerificationBadge() async {
    final verificationData = await _getUserVerificationData();
    final verificationCount = verificationData['verificationCount'] as int;

    if (verificationCount == 0) {
      // No verifications - show warning
      return CircleAvatar(
        radius: 14,
        backgroundColor: Colors.red,
        child: const Icon(Icons.warning, color: Colors.white, size: 18),
      );
    } else if (verificationCount == 1) {
      // 1 verification - yellow badge
      return CircleAvatar(
        radius: 14,
        backgroundColor: Colors.amber,
        child: const Icon(Icons.verified, color: Colors.white, size: 18),
      );
    } else if (verificationCount == 2) {
      // 2 verifications - orange badge
      return CircleAvatar(
        radius: 14,
        backgroundColor: Colors.orange,
        child: const Icon(Icons.verified, color: Colors.white, size: 18),
      );
    } else {
      // 3 verifications - green badge
      return CircleAvatar(
        radius: 14,
        backgroundColor: Colors.green,
        child: const Icon(Icons.verified, color: Colors.white, size: 18),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F1E8),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header with row layout
            _buildProfileHeader(),

            const SizedBox(height: 24),

            // Profile options
            Expanded(
              child: Column(
                children: [
                  _buildProfileOption(
                    icon: Icons.inventory_2_outlined,
                    title: 'My Items',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const MyItemsScreen()),
                      );
                    },
                  ),
                  _buildProfileOption(
                    icon: Icons.verified_user_outlined,
                    title: 'Profile Verification',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(builder: (_) => const ProfileVerificationScreen()),
                      );
                    },
                  ),
                  _buildProfileOption(
                    icon: Icons.edit_outlined,
                    title: 'Edit Profile',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (_) => EditProfileScreen(
                            onProfileUpdated: _onProfileUpdated,
                          ),
                        ),
                      );
                    },
                  ),
                  _buildProfileOption(
                    icon: Icons.settings_outlined,
                    title: 'Settings',
                    onTap: () {
                      // TODO: Navigate to settings
                    },
                  ),
                  _buildProfileOption(
                    icon: Icons.help_outline,
                    title: 'Help & Support',
                    onTap: () {
                      // TODO: Navigate to help
                    },
                  ),
                  _buildProfileOption(
                    icon: Icons.logout,
                    title: 'Sign Out',
                    onTap: () => _handleSignOut(context),
                    isDestructive: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return FutureBuilder<Map<String, dynamic>?>(
      key: ValueKey(_profileUpdateCounter), // This will force rebuild when profile updates
      future: _getUserData(),
      builder: (context, snapshot) {
        final userData = snapshot.data;
        final user = AuthService.currentUser;

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFA8C5B8),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Row(
            children: [
              // Profile picture with verification badge
              Stack(
                children: [
                  CircleAvatar(
                    radius: 35,
                    backgroundColor: const Color(0xFF2D5A5A),
                    backgroundImage: userData?['profileImageUrl'] != null
                        ? NetworkImage(userData!['profileImageUrl'])
                        : (user?.photoURL != null
                            ? NetworkImage(user!.photoURL!)
                            : null),
                    child: (userData?['profileImageUrl'] == null && user?.photoURL == null)
                        ? const Icon(
                            Icons.person,
                            size: 35,
                            color: Colors.white,
                          )
                        : null,
                  ),
                  // Verification badge
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: FutureBuilder<Widget>(
                      future: _buildVerificationBadge(),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          return GestureDetector(
                            onTap: () => _showVerificationModal(context),
                            child: snapshot.data!,
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ),
                ],
              ),

              const SizedBox(width: 16),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userData?['displayName'] ?? user?.displayName ?? 'User',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      user?.email ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                    // const SizedBox(height: 8),
                    // // Verification status text
                    // FutureBuilder<String>(
                    //   future: _getVerificationStatusText(),
                    //   builder: (context, snapshot) {
                    //     if (snapshot.hasData) {
                    //       return Text(
                    //         snapshot.data!,
                    //         style: const TextStyle(
                    //           fontSize: 12,
                    //           color: Color(0xFF2D5A5A),
                    //           fontWeight: FontWeight.w500,
                    //         ),
                    //       );
                    //     }
                    //     return const SizedBox.shrink();
                    //   },
                    // ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<Map<String, dynamic>?> _getUserData() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return null;

      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (doc.exists) {
        return doc.data();
      }
    } catch (e) {
      debugPrint('Error fetching user data: $e');
    }
    return null;
  }

  String _getDocumentSubtitle(dynamic documentStatus, Map<String, dynamic>? userData) {
    if (documentStatus == 'true') {
      return 'Document verified by admin';
    } else if (documentStatus == 'pending') {
      return 'Document under verification';
    } else {
      return 'Upload your ID document';
    }
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isDestructive ? const Color(0xFFE74C3C) : const Color(0xFF5A8A8A),
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: isDestructive ? const Color(0xFFE74C3C) : const Color(0xFF2D5A5A),
                  ),
                ),
                const Spacer(),
                if (!isDestructive)
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Color(0xFF5A8A8A),
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showVerificationModal(BuildContext context) async {
    final verificationData = await _getUserVerificationData();
    final verificationCount = verificationData['verificationCount'] as int;
    final emailVerified = verificationData['emailVerified'] as bool;
    final phoneVerified = verificationData['phoneVerified'] as bool;
    final documentVerified = verificationData['documentVerified'] as bool;
    final documentStatus = verificationData['documentStatus'];
    final userData = verificationData['userData'] as Map<String, dynamic>?;

    if (!mounted || !context.mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (_) => Container(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with verification badge
            Row(
              children: [
                FutureBuilder<Widget>(
                  future: _buildVerificationBadge(),
                  builder: (context, snapshot) {
                    return snapshot.data ?? const Icon(Icons.warning, color: Colors.red, size: 48);
                  },
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getVerificationTitle(verificationCount),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2D5A5A),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$verificationCount of 3 verifications completed',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF5A8A8A),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Verification status list
            _buildVerificationItem(
              icon: Icons.email,
              title: 'Email Verification',
              isVerified: emailVerified,
              subtitle: emailVerified
                  ? 'Email verified successfully'
                  : 'Verify your email address',
            ),

            const SizedBox(height: 12),

            _buildVerificationItem(
              icon: Icons.phone,
              title: 'Phone Verification',
              isVerified: phoneVerified,
              subtitle: phoneVerified
                  ? 'Phone number verified'
                  : 'Verify your phone number',
            ),

            const SizedBox(height: 12),

            _buildVerificationItem(
              icon: Icons.description,
              title: 'Document Verification',
              isVerified: documentVerified,
              subtitle: _getDocumentSubtitle(documentStatus, userData),
            ),

            const SizedBox(height: 24),

            // Action button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context); // Close modal
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const ProfileVerificationScreen()),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3AD29F),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  verificationCount == 3 ? 'View Verification Details' : 'Complete Verification',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  String _getVerificationTitle(int verificationCount) {
    switch (verificationCount) {
      case 0:
        return 'Profile Not Verified';
      case 1:
        return 'Partially Verified';
      case 2:
        return 'Almost Verified';
      case 3:
        return 'Fully Verified';
      default:
        return 'Verification Status';
    }
  }

  Widget _buildVerificationItem({
    required IconData icon,
    required String title,
    required bool isVerified,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isVerified
            ? const Color(0xFF3AD29F).withValues(alpha: 0.1)
            : const Color(0xFFF5F1E8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isVerified
              ? const Color(0xFF3AD29F)
              : const Color(0xFFE0E0E0),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: isVerified
                ? const Color(0xFF3AD29F)
                : const Color(0xFF5A8A8A),
            size: 24,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isVerified
                        ? const Color(0xFF3AD29F)
                        : const Color(0xFF2D5A5A),
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF5A8A8A),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            isVerified ? Icons.check_circle : Icons.radio_button_unchecked,
            color: isVerified
                ? const Color(0xFF3AD29F)
                : const Color(0xFFE0E0E0),
            size: 20,
          ),
        ],
      ),
    );
  }
}
