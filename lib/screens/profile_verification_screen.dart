import 'dart:io';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import '../services/auth_service.dart';
import '../services/otp_service.dart';

class ProfileVerificationScreen extends StatefulWidget {
  const ProfileVerificationScreen({super.key});

  @override
  State<ProfileVerificationScreen> createState() => _ProfileVerificationScreenState();
}

class _ProfileVerificationScreenState extends State<ProfileVerificationScreen> {
  final ImagePicker _picker = ImagePicker();

  // Controllers
  final _phoneController = TextEditingController();
  final _phoneOtpController = TextEditingController();
  final _emailOtpController = TextEditingController();

  // User data
  Map<String, dynamic>? _userData;
  bool _isLoading = true;

  // Phone verification
  bool _isPhoneVerified = false;
  bool _phoneOtpSent = false;
  bool _isPhoneLoading = false;

  // Email verification
  bool _isEmailVerified = false;
  bool _emailOtpSent = false;
  bool _isEmailLoading = false;

  // Document verification
  String? _selectedDocumentType;
  XFile? _selectedDocument;
  bool _isDocumentUploading = false;
  String _documentVerificationStatus = 'false'; // 'false', 'pending', 'true'

  final List<String> _documentTypes = [
    'Aadhar Card',
    'Driving License',
    'PAN Card',
    'Passport',
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _phoneOtpController.dispose();
    _emailOtpController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return;

      final userData = await AuthService.getUserData(user.uid);

      if (userData != null) {
        setState(() {
          _userData = userData;
          _isPhoneVerified = _userData?['phoneVerified'] ?? false;
          _isEmailVerified = _userData?['emailVerified'] ?? false;

          // Handle document verification status (now stored as string)
          final docVerified = _userData?['documentVerified'];
          if (docVerified == 'true') {
            _documentVerificationStatus = 'true';
          } else if (docVerified == 'pending') {
            _documentVerificationStatus = 'pending';
          } else {
            _documentVerificationStatus = 'false';
          }

          // Only pre-fill document type if status is not pending (since we clear it after upload)
          if (_documentVerificationStatus != 'pending') {
            _selectedDocumentType = _userData?['documentType'];
          }

          // Pre-fill phone if available
          if (_userData?['phoneNumber'] != null) {
            _phoneController.text = _userData!['phoneNumber'];
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to load user data: ${e.toString()}');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _sendPhoneOtp() async {
    if (_phoneController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter your phone number');
      return;
    }

    setState(() => _isPhoneLoading = true);

    try {
      // TODO: Implement actual SMS OTP service (Firebase Auth Phone, Twilio, etc.)
      // For now, we'll simulate OTP sending
      await Future.delayed(const Duration(seconds: 2));

      setState(() {
        _phoneOtpSent = true;
      });

      _showSuccessSnackBar('OTP sent to ${_phoneController.text}');
    } catch (e) {
      _showErrorSnackBar('Failed to send OTP: ${e.toString()}');
    } finally {
      setState(() => _isPhoneLoading = false);
    }
  }

  Future<void> _verifyPhoneOtp() async {
    if (_phoneOtpController.text.trim().isEmpty) {
      _showErrorSnackBar('Please enter the OTP');
      return;
    }

    setState(() => _isPhoneLoading = true);

    try {
      // TODO: Implement actual OTP verification
      // For demo purposes, accept "123456" as valid OTP
      if (_phoneOtpController.text.trim() == '123456') {
        final user = AuthService.currentUser;
        if (user != null) {
          await AuthService.updateUserData(user.uid, {
            'phoneNumber': _phoneController.text.trim(),
            'phoneVerified': true,
          });

          setState(() {
            _isPhoneVerified = true;
            _phoneOtpSent = false;
          });

          _showSuccessSnackBar('Phone number verified successfully!');
        }
      } else {
        _showErrorSnackBar('Invalid OTP. Please try again.');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to verify OTP: ${e.toString()}');
    } finally {
      setState(() => _isPhoneLoading = false);
    }
  }

  Future<void> _sendEmailOtp() async {
    setState(() => _isEmailLoading = true);

    try {
      final userEmail = _userData?['email'] as String?;
      final userName = _userData?['displayName'] as String?;

      print('🔍 DEBUG: Starting email OTP send process');
      print('🔍 DEBUG: User email: $userEmail');
      print('🔍 DEBUG: User name: $userName');
      print('🔍 DEBUG: User data: $_userData');

      if (userEmail == null || userEmail.isEmpty) {
        print('❌ DEBUG: No email address found');
        throw Exception('No email address found for your account');
      }

      print('🔍 DEBUG: Calling OTPService.sendEmailOTP...');

      // Send OTP using Firebase Functions
      final result = await OTPService.sendEmailOTP(
        email: userEmail,
        userName: userName,
      );

      print('🔍 DEBUG: OTP service result: $result');

      if (result['success'] == true) {
        setState(() {
          _emailOtpSent = true;
        });

        print('✅ DEBUG: OTP sent successfully');
        _showSuccessSnackBar('OTP sent to $userEmail');
      } else {
        print('❌ DEBUG: OTP service returned failure: ${result['message']}');
        throw Exception(result['message'] ?? 'Failed to send OTP');
      }
    } catch (e, stackTrace) {
      print('❌ DEBUG: Exception caught in _sendEmailOtp:');
      print('❌ DEBUG: Error: $e');
      print('❌ DEBUG: Error type: ${e.runtimeType}');
      print('❌ DEBUG: Stack trace: $stackTrace');

      // Show full error in console for debugging
      final fullError = e.toString();
      print('❌ DEBUG: Full error message: $fullError');

      // Show truncated error in snackbar for user
      final userError = fullError.replaceFirst('Exception: ', '');
      _showErrorSnackBar('Failed to send email OTP: $userError');
    } finally {
      setState(() => _isEmailLoading = false);
    }
  }

  Future<void> _verifyEmailOtp() async {
    final otpText = _emailOtpController.text.trim();

    if (otpText.isEmpty) {
      _showErrorSnackBar('Please enter the OTP');
      return;
    }

    if (!OTPService.isValidOTP(otpText)) {
      _showErrorSnackBar('Please enter a valid 6-digit OTP');
      return;
    }

    setState(() => _isEmailLoading = true);

    try {
      final userEmail = _userData?['email'] as String?;

      if (userEmail == null || userEmail.isEmpty) {
        throw Exception('No email address found for your account');
      }

      // Verify OTP using Firebase Functions
      final result = await OTPService.verifyEmailOTP(
        email: userEmail,
        otp: otpText,
      );

      if (result['success'] == true) {
        final user = AuthService.currentUser;
        if (user != null) {
          // Update user data in Firestore
          await AuthService.updateUserData(user.uid, {
            'emailVerified': true,
          });

          setState(() {
            _isEmailVerified = true;
            _emailOtpSent = false;
          });

          // Clear the OTP input
          _emailOtpController.clear();

          _showSuccessSnackBar('Email verified successfully!');
        }
      } else {
        throw Exception(result['message'] ?? 'Failed to verify OTP');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to verify email OTP: ${e.toString().replaceFirst('Exception: ', '')}');
    } finally {
      setState(() => _isEmailLoading = false);
    }
  }

  Future<void> _pickDocument() async {
    if (_selectedDocumentType == null) {
      _showErrorSnackBar('Please select a document type first');
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedDocument = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image: ${e.toString()}');
    }
  }

  Future<void> _takeDocumentPhoto() async {
    if (_selectedDocumentType == null) {
      _showErrorSnackBar('Please select a document type first');
      return;
    }

    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedDocument = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: ${e.toString()}');
    }
  }

  Future<void> _uploadDocument() async {
    if (_selectedDocument == null || _selectedDocumentType == null) {
      _showErrorSnackBar('Please select a document type and upload an image');
      return;
    }

    setState(() => _isDocumentUploading = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Upload document to Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('documents')
          .child(user.uid)
          .child('${_selectedDocumentType?.toLowerCase().replaceAll(' ', '_')}.jpg');

      final uploadTask = storageRef.putFile(File(_selectedDocument!.path));
      final snapshot = await uploadTask;
      final downloadUrl = await snapshot.ref.getDownloadURL();

      // Update user document in Firestore
      await AuthService.updateUserData(user.uid, {
        'documentType': _selectedDocumentType,
        'documentUrl': downloadUrl,
        'documentVerified': 'pending', // Pending admin verification
        'documentUploadedAt': FieldValue.serverTimestamp(),
      });

      setState(() {
        _documentVerificationStatus = 'pending'; // Pending admin verification
        _selectedDocument = null; // Clear selected image
        _selectedDocumentType = null; // Clear selected document type
      });

      _showSuccessSnackBar('Document uploaded successfully! Verification pending.');
    } catch (e) {
      _showErrorSnackBar('Failed to upload document: ${e.toString()}');
    } finally {
      setState(() => _isDocumentUploading = false);
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  // Helper methods for document verification status
  IconData _getDocumentIcon() {
    switch (_documentVerificationStatus) {
      case 'true':
        return Icons.verified;
      case 'pending':
        return Icons.hourglass_empty;
      case 'false':
      default:
        return Icons.description;
    }
  }

  Color _getDocumentIconColor() {
    switch (_documentVerificationStatus) {
      case 'true':
        return const Color(0xFF3AD29F);
      case 'pending':
        return const Color(0xFFFF9800);
      case 'false':
      default:
        return const Color(0xFF5A8A8A);
    }
  }

  Widget _buildDocumentStatusBadge() {
    switch (_documentVerificationStatus) {
      case 'true':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF3AD29F),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text(
            'Verified',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      case 'pending':
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFFF9800),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text(
            'Pending',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
      case 'false':
      default:
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFF5A8A8A),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Text(
            'Not Verified',
            style: TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        );
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF3AD29F),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: const Color(0xFFF5F1E8),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF5F1E8),
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFF2D5A5A),
            ),
          ),
          title: const Text(
            'Profile Verification',
            style: TextStyle(
              color: Color(0xFF2D5A5A),
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFF3AD29F),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF2D5A5A),
          ),
        ),
        title: const Text(
          'Profile Verification',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info Section
            _buildUserInfoSection(),
            const SizedBox(height: 18),

            // Phone Verification Section
            _buildPhoneVerificationSection(),
            const SizedBox(height: 18),

            // Email Verification Section
            _buildEmailVerificationSection(),
            const SizedBox(height: 18),

            // Document Verification Section
            _buildDocumentVerificationSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Profile Information',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D5A5A),
            ),
          ),
          const SizedBox(height: 16),

          // Profile picture and basic info
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: const Color(0xFF2D5A5A),
                backgroundImage: _userData?['photoURL'] != null
                    ? NetworkImage(_userData!['photoURL'])
                    : null,
                child: _userData?['photoURL'] == null
                    ? const Icon(
                        Icons.person,
                        size: 30,
                        color: Colors.white,
                      )
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _userData?['displayName'] ?? 'User',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _userData?['email'] ?? '',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPhoneVerificationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isPhoneVerified ? Icons.verified : Icons.phone,
                color: _isPhoneVerified ? const Color(0xFF3AD29F) : const Color(0xFF5A8A8A),
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Phone Verification',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D5A5A),
                ),
              ),
              const Spacer(),
              if (_isPhoneVerified)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3AD29F),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Verified',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),

          if (!_isPhoneVerified) ...[
            const SizedBox(height: 16),

            // Phone number input
            TextFormField(
              controller: _phoneController,
              keyboardType: TextInputType.phone,
              enabled: !_phoneOtpSent,
              style: const TextStyle(
                color: Color(0xFF2D5A5A),
                fontSize: 16,
              ),
              decoration: InputDecoration(
                hintText: 'Enter your phone number',
                hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
                prefixText: '+91 ',
                prefixStyle: const TextStyle(
                  color: Color(0xFF2D5A5A),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                filled: true,
                fillColor: const Color(0xFFF5F1E8),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
                ),
                contentPadding: const EdgeInsets.all(16),
              ),
            ),

            const SizedBox(height: 16),

            if (!_phoneOtpSent) ...[
              // Send OTP button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isPhoneLoading ? null : _sendPhoneOtp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF3AD29F),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isPhoneLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Send OTP',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ] else ...[
              // OTP input and verify
              TextFormField(
                controller: _phoneOtpController,
                keyboardType: TextInputType.number,
                maxLength: 6,
                style: const TextStyle(
                  color: Color(0xFF2D5A5A),
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter 6-digit OTP',
                  hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
                  filled: true,
                  fillColor: const Color(0xFFF5F1E8),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                  counterText: '',
                ),
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isPhoneLoading ? null : _verifyPhoneOtp,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF3AD29F),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isPhoneLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text(
                              'Verify OTP',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _phoneOtpSent = false;
                        _phoneOtpController.clear();
                      });
                    },
                    child: const Text(
                      'Resend',
                      style: TextStyle(
                        color: Color(0xFF5A8A8A),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),
              const Text(
                'Demo: Use OTP "123456" for verification',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF5A8A8A),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ] else ...[
            const SizedBox(height: 16),
            Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Color(0xFF3AD29F),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Phone number verified: ${_userData?['phoneNumber']}',
                  style: const TextStyle(
                    color: Color(0xFF3AD29F),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildEmailVerificationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _isEmailVerified ? Icons.verified : Icons.email,
                color: _isEmailVerified ? const Color(0xFF3AD29F) : const Color(0xFF5A8A8A),
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Email Verification',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D5A5A),
                ),
              ),
              const Spacer(),
              if (_isEmailVerified)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3AD29F),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Verified',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          if (!_isEmailVerified) ...[
            Text(
              'Email: ${_userData?['email'] ?? 'Not available'}',
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF2D5A5A),
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 16),

            if (!_emailOtpSent) ...[
              // Send email OTP button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isEmailLoading ? null : _sendEmailOtp,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF3AD29F),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isEmailLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Text(
                          'Send Email OTP',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                ),
              ),
            ] else ...[
              // Email OTP input and verify
              TextFormField(
                controller: _emailOtpController,
                keyboardType: TextInputType.number,
                maxLength: 6,
                style: const TextStyle(
                  color: Color(0xFF2D5A5A),
                  fontSize: 16,
                ),
                decoration: InputDecoration(
                  hintText: 'Enter 6-digit OTP from email',
                  hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
                  filled: true,
                  fillColor: const Color(0xFFF5F1E8),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
                  ),
                  contentPadding: const EdgeInsets.all(16),
                  counterText: '',
                ),
              ),

              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isEmailLoading ? null : _verifyEmailOtp,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF3AD29F),
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: _isEmailLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text(
                              'Verify OTP',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _emailOtpSent = false;
                        _emailOtpController.clear();
                      });
                    },
                    child: const Text(
                      'Resend',
                      style: TextStyle(
                        color: Color(0xFF5A8A8A),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),
              const Text(
                'Demo: Use OTP "123456" for verification',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFF5A8A8A),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ] else ...[
            Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: Color(0xFF3AD29F),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Email verified: ${_userData?['email']}',
                  style: const TextStyle(
                    color: Color(0xFF3AD29F),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDocumentVerificationSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getDocumentIcon(),
                color: _getDocumentIconColor(),
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Document Verification',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D5A5A),
                ),
              ),
              const Spacer(),
              _buildDocumentStatusBadge(),
            ],
          ),

          const SizedBox(height: 16),

          if (_documentVerificationStatus == 'false') ...[
            // Document type dropdown
            const Text(
              'Select Document Type',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2D5A5A),
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F1E8),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE0E0E0)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: _selectedDocumentType,
                  hint: const Text(
                    'Choose document type',
                    style: TextStyle(color: Color(0xFF5A8A8A)),
                  ),
                  isExpanded: true,
                  icon: const Icon(
                    Icons.keyboard_arrow_down,
                    color: Color(0xFF5A8A8A),
                  ),
                  style: const TextStyle(
                    color: Color(0xFF2D5A5A),
                    fontSize: 16,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _selectedDocumentType = value;
                      _selectedDocument = null; // Reset selected document
                    });
                  },
                  items: _documentTypes.map((String type) {
                    return DropdownMenuItem<String>(
                      value: type,
                      child: Text(type),
                    );
                  }).toList(),
                ),
              ),
            ),

            const SizedBox(height: 20),

            // Document upload section
            if (_selectedDocumentType != null) ...[
              const Text(
                'Upload Document',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2D5A5A),
                ),
              ),
              const SizedBox(height: 12),

              // Upload options
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _pickDocument,
                      icon: const Icon(Icons.photo_library, color: Colors.white),
                      label: const Text(
                        'Gallery',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF5A8A8A),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: _takeDocumentPhoto,
                      icon: const Icon(Icons.camera_alt, color: Colors.white),
                      label: const Text(
                        'Camera',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF5A8A8A),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Show selected document
              if (_selectedDocument != null) ...[
                Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xFFE0E0E0)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.file(
                      File(_selectedDocument!.path),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Upload button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isDocumentUploading ? null : _uploadDocument,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3AD29F),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isDocumentUploading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text(
                            'Upload Document',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ),
              ],
            ],
          ] else if (_documentVerificationStatus == 'pending') ...[
            // Pending approval status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFFFF9800).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xFFFF9800).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.hourglass_empty,
                    color: Color(0xFFFF9800),
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Document Under Verification',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFFF9800),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your ${_userData?['documentType'] ?? 'document'} is being reviewed by our admin team.',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF5A8A8A),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF9800).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Color(0xFFFF9800),
                          size: 16,
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Please wait for admin approval. You cannot upload a new document during verification.',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFFF9800),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ] else if (_documentVerificationStatus == 'true') ...[
            // Verified status
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF3AD29F).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: const Color(0xFF3AD29F).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.verified,
                    color: Color(0xFF3AD29F),
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Document Verified',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF3AD29F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your ${_userData?['documentType'] ?? 'document'} has been successfully verified by our admin team.',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF5A8A8A),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}