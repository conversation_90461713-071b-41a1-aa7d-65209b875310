import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth_service.dart';
import 'home_screen.dart';
import 'login_screen.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _controller = PageController();
  int _currentPage = 0;

  final List<Map<String, String>> _pages = [
    {
      'title': 'Welcome to EvenOut',
      'desc': 'Exchange goods and products easily with others in your community.'
    },
    {
      'title': 'List Your Items',
      'desc': 'Post items you no longer need and find something you want.'
    },
    {
      'title': 'Swap & Connect',
      'desc': 'Chat, swap, and build a sustainable lifestyle together!'
    },
  ];

  void _nextPage() async {
    if (_currentPage < _pages.length - 1) {
      _controller.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      );
    } else {
      // Mark onboarding as completed
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('is_first_time', false);

      if (mounted) {
        // Check if user is already logged in
        if (AuthService.isLoggedIn) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (_) => const HomeScreen(showWelcomeMessage: false)),
          );
        } else {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (_) => const LoginScreen()),
          );
        }
      }
    }
  }

  Widget _buildIllustration(int index) {
    // Create different illustrations for each page
    switch (index) {
      case 0:
        return _buildWelcomeIllustration();
      case 1:
        return _buildListItemsIllustration();
      case 2:
        return _buildSwapConnectIllustration();
      default:
        return _buildWelcomeIllustration();
    }
  }

  Widget _buildWelcomeIllustration() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Three people illustration
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPerson(const Color(0xFF4A5568), Icons.person),
              _buildPerson(const Color(0xFF2D3748), Icons.person_outline),
              _buildPerson(const Color(0xFF1A202C), Icons.person),
            ],
          ),
          const SizedBox(height: 20),
          // Exchange symbol
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2D5A5A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.swap_horiz,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItemsIllustration() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Items grid
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildItem(Icons.phone_android, const Color(0xFF4A5568)),
              _buildItem(Icons.book, const Color(0xFF2D3748)),
              _buildItem(Icons.sports_basketball, const Color(0xFF1A202C)),
            ],
          ),
          const SizedBox(height: 20),
          // Add symbol
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2D5A5A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.add,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSwapConnectIllustration() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Connected people
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPerson(const Color(0xFF4A5568), Icons.person),
              Container(
                padding: const EdgeInsets.all(4),
                child: const Icon(
                  Icons.favorite,
                  color: Color(0xFF2D5A5A),
                  size: 20,
                ),
              ),
              _buildPerson(const Color(0xFF2D3748), Icons.person_outline),
            ],
          ),
          const SizedBox(height: 20),
          // Chat symbol
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF2D5A5A),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.chat_bubble_outline,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerson(Color color, IconData icon) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(25),
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: 30,
      ),
    );
  }

  Widget _buildItem(IconData icon, Color color) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        icon,
        color: Colors.white,
        size: 24,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return PopScope(
      canPop: _currentPage == 0,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop && _currentPage > 0) {
          _controller.previousPage(
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut,
          );
        }
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F1E8), // Cream background
        body: SafeArea(
          child: Stack(
            children: [
              PageView.builder(
                controller: _controller,
                itemCount: _pages.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Column(
                      children: [
                        const SizedBox(height: 60),
                        // Large illustration container
                        Expanded(
                          flex: 3,
                          child: Container(
                            width: double.infinity,
                            margin: const EdgeInsets.symmetric(horizontal: 16),
                            decoration: BoxDecoration(
                              color: const Color(0xFFA8C5B8), // Sage green
                              borderRadius: BorderRadius.circular(24),
                            ),
                            child: Stack(
                              children: [
                                // Background frame effect
                                Positioned(
                                  top: 20,
                                  left: 20,
                                  right: 20,
                                  bottom: 20,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: const Color(0xFFF5F1E8).withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: const Color(0xFF8BB5A8),
                                        width: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                // Illustration content
                                Center(
                                  child: _buildIllustration(index),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(height: 60),
                        // Text content
                        Expanded(
                          flex: 1,
                          child: Column(
                            children: [
                              Text(
                                _pages[index]['title']!,
                                style: theme.textTheme.headlineMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: const Color(0xFF2D5A5A), // Dark teal
                                  fontSize: 32,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16),
                                child: Text(
                                  _pages[index]['desc']!,
                                  style: theme.textTheme.bodyLarge?.copyWith(
                                    color: const Color(0xFF5A8A8A), // Medium teal
                                    fontSize: 16,
                                    height: 1.5,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 100), // Space for bottom navigation
                      ],
                    ),
                  );
                },
              ),
              // Bottom navigation
              Positioned(
                left: 0,
                right: 0,
                bottom: 40,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Page indicators
                      Row(
                        children: List.generate(
                          _pages.length,
                          (index) => AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            margin: const EdgeInsets.only(right: 8),
                            width: _currentPage == index ? 24 : 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _currentPage == index
                                  ? const Color(0xFF2D5A5A) // Dark teal for active
                                  : const Color(0xFFE6C068), // Yellow for inactive
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),
                      ),
                      // Next/Get Started button
                      AnimatedContainer(
                        duration: const Duration(milliseconds: 400),
                        curve: Curves.easeInOut,
                        width: _currentPage == _pages.length - 1 ? 160 : 64,
                        height: 64,
                        decoration: BoxDecoration(
                          color: const Color(0xFF3AD29F), // Teal green
                          borderRadius: BorderRadius.circular(32),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(0xFF3AD29F).withValues(alpha: 0.3),
                              blurRadius: 12,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: _nextPage,
                            borderRadius: BorderRadius.circular(32),
                            child: Center(
                              child: AnimatedSwitcher(
                                duration: const Duration(milliseconds: 300),
                                child: _currentPage == _pages.length - 1
                                    ? const Text(
                                        'Get Started',
                                        key: ValueKey('get_started'),
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      )
                                    : const Icon(
                                        Icons.arrow_forward_rounded,
                                        key: ValueKey('arrow'),
                                        color: Colors.white,
                                        size: 28,
                                      ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}