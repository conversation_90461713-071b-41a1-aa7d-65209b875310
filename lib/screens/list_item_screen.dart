import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';

class ListItemScreen extends StatefulWidget {
  const ListItemScreen({super.key});

  @override
  State<ListItemScreen> createState() => _ListItemScreenState();
}

class _ListItemScreenState extends State<ListItemScreen> {
  final _formKey = GlobalKey<FormState>();
  final _itemNameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _conditionDescriptionController = TextEditingController();
  final _priceController = TextEditingController();

  final List<XFile> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  String? _selectedCategory;
  String? _selectedCondition;
  bool _isPriceNegotiable = false;
  bool _isLoading = false;

  final List<String> _categories = [
    'Electronics',
    'Clothing & Fashion',
    'Books & Media',
    'Sports & Outdoors',
    'Home & Garden',
    'Toys & Games',
    'Automotive',
    'Health & Beauty',
    'Collectibles',
    'Other',
  ];

  final List<String> _conditions = [
    'Brand New',
    'Like New',
    'Good',
    'Fair',
    'Poor',
  ];

  @override
  void dispose() {
    _itemNameController.dispose();
    _descriptionController.dispose();
    _conditionDescriptionController.dispose();
    _priceController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
          // Limit to maximum 5 images
          if (_selectedImages.length > 5) {
            _selectedImages.removeRange(5, _selectedImages.length);
          }
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick images. Please try again.');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _submitItem() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedImages.isEmpty) {
      _showErrorSnackBar('Please add at least one photo of your item.');
      return;
    }

    if (_selectedCategory == null) {
      _showErrorSnackBar('Please select a category.');
      return;
    }

    if (_selectedCondition == null) {
      _showErrorSnackBar('Please select the item condition.');
      return;
    }

    setState(() => _isLoading = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) {
        throw 'User not authenticated';
      }

      // Generate unique item ID
      final itemId = FirebaseFirestore.instance.collection('items').doc().id;

      // Upload images to Firebase Storage
      final List<String> imageUrls = [];
      for (int i = 0; i < _selectedImages.length; i++) {
        final imageUrl = await _uploadImage(_selectedImages[i], itemId, i);
        imageUrls.add(imageUrl);
      }

      // Parse price
      double? price;
      if (_priceController.text.isNotEmpty) {
        price = double.tryParse(_priceController.text);
      }

      // Create item document in Firestore
      final itemData = {
        'itemId': itemId,
        'itemName': _itemNameController.text.trim(),
        'description': _descriptionController.text.trim(),
        'category': _selectedCategory!,
        'condition': _selectedCondition!,
        'conditionDescription': _conditionDescriptionController.text.trim().isEmpty
            ? null
            : _conditionDescriptionController.text.trim(),
        'price': price,
        'isPriceNegotiable': _isPriceNegotiable,
        'images': imageUrls,
        'userId': user.uid,
        'userEmail': user.email,
        'userName': user.displayName ?? user.email?.split('@')[0] ?? 'User',
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'views': 0,
        'likes': 0,
      };

      await FirebaseFirestore.instance
          .collection('items')
          .doc(itemId)
          .set(itemData);

      if (mounted) {
        // Clear form
        _clearForm();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Item listed successfully!'),
            backgroundColor: Color(0xFF3AD29F),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to list item: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<String> _uploadImage(XFile imageFile, String itemId, int index) async {
    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Create a reference to Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('items')
          .child(user.uid)
          .child(itemId)
          .child('image_$index.jpg');

      // Upload the file
      final uploadTask = storageRef.putFile(File(imageFile.path));
      final snapshot = await uploadTask;

      // Get the download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      throw 'Failed to upload image: $e';
    }
  }

  void _clearForm() {
    _itemNameController.clear();
    _descriptionController.clear();
    _conditionDescriptionController.clear();
    _priceController.clear();
    setState(() {
      _selectedImages.clear();
      _selectedCategory = null;
      _selectedCondition = null;
      _isPriceNegotiable = false;
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  void _viewImage(int index) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImageViewerScreen(
          imagePath: _selectedImages[index].path,
          imageIndex: index,
          totalImages: _selectedImages.length,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F1E8),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // Header with Post button
            Container(
              padding: const EdgeInsets.all(24),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'List Item',
                    style: TextStyle(
                      color: Color(0xFF2D5A5A),
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                  ElevatedButton(
                    onPressed: _isLoading ? null : _submitItem,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3AD29F),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                        : const Text(
                            'Post',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                  ),
                ],
              ),
            ),

            // Scrollable content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Photo Section
                    _buildPhotoSection(),
                    const SizedBox(height: 32),

                    // Item Details
                    _buildItemDetailsSection(),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Photos',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'Add up to 5 photos of your item',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF5A8A8A),
          ),
        ),
        const SizedBox(height: 16),

        // Photo carousel
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length + 1,
            itemBuilder: (context, index) {
              if (index == _selectedImages.length) {
                // Add photo button
                return _buildAddPhotoButton();
              } else {
                // Display selected image
                return _buildImageCard(index);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAddPhotoButton() {
    return Container(
      width: 120,
      height: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFE0E0E0),
          width: 2,
          style: BorderStyle.solid,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _selectedImages.length < 5 ? _pickImages : null,
          borderRadius: BorderRadius.circular(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_photo_alternate_outlined,
                size: 32,
                color: _selectedImages.length < 5
                    ? const Color(0xFF3AD29F)
                    : const Color(0xFFE0E0E0),
              ),
              const SizedBox(height: 8),
              Text(
                'Add Photo',
                style: TextStyle(
                  fontSize: 12,
                  color: _selectedImages.length < 5
                      ? const Color(0xFF3AD29F)
                      : const Color(0xFFE0E0E0),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageCard(int index) {
    return Container(
      width: 120,
      height: 120,
      margin: const EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          GestureDetector(
            onTap: () => _viewImage(index),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Image.file(
                File(_selectedImages[index].path),
                width: 120,
                height: 120,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: Color(0xFFE74C3C),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemDetailsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item Name
        _buildTextField(
          controller: _itemNameController,
          label: 'Item Name',
          hint: 'What are you listing?',
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Please enter the item name';
            }
            return null;
          },
        ),

        const SizedBox(height: 24),

        // Description
        _buildTextField(
          controller: _descriptionController,
          label: 'Description',
          hint: 'Describe your item in detail...',
          maxLines: 4,
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Please enter a description';
            }
            if (value!.length < 10) {
              return 'Description should be at least 10 characters';
            }
            return null;
          },
        ),

        const SizedBox(height: 24),

        // Category Selection
        _buildDropdownField(
          label: 'Category',
          value: _selectedCategory,
          items: _categories,
          onChanged: (value) {
            setState(() {
              _selectedCategory = value;
            });
          },
          hint: 'Select a category',
        ),

        const SizedBox(height: 24),

        // Condition Selection
        _buildDropdownField(
          label: 'Condition',
          value: _selectedCondition,
          items: _conditions,
          onChanged: (value) {
            setState(() {
              _selectedCondition = value;
            });
          },
          hint: 'Select item condition',
        ),

        const SizedBox(height: 24),

        // Condition Description
        _buildTextField(
          controller: _conditionDescriptionController,
          label: 'Condition Details',
          hint: 'Any specific details about the condition...',
          maxLines: 2,
        ),

        const SizedBox(height: 24),

        // Price Section
        _buildPriceSection(),

        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE74C3C)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE74C3C), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),
      ],
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required void Function(String?) onChanged,
    required String hint,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              hint: Text(
                hint,
                style: const TextStyle(color: Color(0xFF5A8A8A)),
              ),
              isExpanded: true,
              icon: const Icon(
                Icons.keyboard_arrow_down,
                color: Color(0xFF5A8A8A),
              ),
              style: const TextStyle(
                color: Color(0xFF2D5A5A),
                fontSize: 16,
              ),
              onChanged: onChanged,
              items: items.map((String item) {
                return DropdownMenuItem<String>(
                  value: item,
                  child: Text(item),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Price',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _priceController,
          keyboardType: TextInputType.number,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: 'Enter price (optional)',
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            prefixText: '\$ ',
            prefixStyle: const TextStyle(
              color: Color(0xFF2D5A5A),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(16),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),

        const SizedBox(height: 16),

        // Price Negotiable Switch
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFFE0E0E0)),
          ),
          child: Row(
            children: [
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Price Negotiable',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'Allow buyers to negotiate the price',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                  ],
                ),
              ),
              Switch(
                value: _isPriceNegotiable,
                onChanged: (value) {
                  setState(() {
                    _isPriceNegotiable = value;
                  });
                },
                activeColor: const Color(0xFF3AD29F),
                activeTrackColor: const Color(0xFF3AD29F).withValues(alpha: 0.3),
                inactiveThumbColor: const Color(0xFFE0E0E0),
                inactiveTrackColor: const Color(0xFFF5F5F5),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _ImageViewerScreen extends StatelessWidget {
  final String imagePath;
  final int imageIndex;
  final int totalImages;

  const _ImageViewerScreen({
    required this.imagePath,
    required this.imageIndex,
    required this.totalImages,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
        ),
        title: Text(
          'Image ${imageIndex + 1} of $totalImages',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
          ),
        ),
      ),
      body: Center(
        child: InteractiveViewer(
          panEnabled: true,
          boundaryMargin: const EdgeInsets.all(20),
          minScale: 0.5,
          maxScale: 4.0,
          child: Image.file(
            File(imagePath),
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}
