// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAiLzibmDLzoqyA9s5RCm9v9F_9JacGCCk',
    appId: '1:1037152015645:android:84d70136a13295892cbdf4',
    messagingSenderId: '1037152015645',
    projectId: 'evenout-b1432',
    storageBucket: 'evenout-b1432.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAkr_fRnBTygVPFWYwR574OPctoIsaQ500',
    appId: '1:1037152015645:ios:bd5f531e9a9d21222cbdf4',
    messagingSenderId: '1037152015645',
    projectId: 'evenout-b1432',
    storageBucket: 'evenout-b1432.firebasestorage.app',
    iosBundleId: 'com.evenout.evenout',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyByFtEO_UqYqWIZD6297kraDAifkPf8crg',
    appId: '1:1037152015645:web:8ec1110d88f040152cbdf4',
    messagingSenderId: '1037152015645',
    projectId: 'evenout-b1432',
    authDomain: 'evenout-b1432.firebaseapp.com',
    storageBucket: 'evenout-b1432.firebasestorage.app',
  );

}