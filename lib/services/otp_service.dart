import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';

class OTPService {
  static final FirebaseFunctions _functions = FirebaseFunctions.instance;

  /// Sends OTP to the specified email address
  ///
  /// [email] - The email address to send OTP to
  /// [userName] - Optional user name for personalization
  ///
  /// Returns a Map with success status, message, and expiration time
  ///
  /// Throws an exception if sending fails
  static Future<Map<String, dynamic>> sendEmailOTP({
    required String email,
    String? userName,
  }) async {
    try {
      print('🔍 OTPService: Starting sendEmailOTP');
      print('🔍 OTPService: Email: $email');
      print('🔍 OTPService: UserName: $userName');

      final callable = _functions.httpsCallable('sendOTPEmail');
      print('🔍 OTPService: Created callable function');

      final requestData = {
        'email': email.toLowerCase().trim(),
        'userName': userName,
      };
      print('🔍 OTPService: Request data: $requestData');

      print('🔍 OTPService: Calling Firebase Function...');
      final result = await callable.call(requestData);
      print('🔍 OTPService: Function call completed');
      print('🔍 OTPService: Raw result: $result');
      print('🔍 OTPService: Result data: ${result.data}');
      print('🔍 OTPService: Result data type: ${result.data.runtimeType}');

      if (result.data['success'] == true) {
        print('✅ OTPService: Success response received');
        return {
          'success': true,
          'message': result.data['message'] ?? 'OTP sent successfully',
          'expiresAt': result.data['expiresAt'],
        };
      } else {
        print('❌ OTPService: Failure response: ${result.data['message']}');
        throw Exception(result.data['message'] ?? 'Failed to send OTP');
      }
    } on FirebaseFunctionsException catch (e) {
      // Handle Firebase Functions specific errors
      print('❌ OTPService: FirebaseFunctionsException caught');
      print('❌ OTPService: Error code: ${e.code}');
      print('❌ OTPService: Error message: ${e.message}');
      print('❌ OTPService: Error details: ${e.details}');

      String errorMessage;

      switch (e.code) {
        case 'invalid-argument':
          errorMessage = 'Invalid email address format';
          break;
        case 'permission-denied':
          errorMessage = 'Permission denied. Please try again';
          break;
        case 'unavailable':
          errorMessage = 'Service temporarily unavailable. Please try again later';
          break;
        case 'deadline-exceeded':
          errorMessage = 'Request timeout. Please check your internet connection';
          break;
        default:
          errorMessage = e.message ?? 'Failed to send OTP. Please try again';
      }

      print('❌ OTPService: Throwing exception: $errorMessage');
      throw Exception(errorMessage);
    } catch (e) {
      // Handle other errors
      print('❌ OTPService: General exception caught');
      print('❌ OTPService: Exception type: ${e.runtimeType}');
      print('❌ OTPService: Exception message: $e');

      if (e.toString().contains('Failed to send OTP:')) {
        // Extract the actual error message from Firebase Functions
        final errorMsg = e.toString().replaceFirst('Exception: ', '');
        print('❌ OTPService: Extracted error message: $errorMsg');
        throw Exception(errorMsg);
      }

      print('❌ OTPService: Throwing network error');
      throw Exception('Network error. Please check your internet connection and try again');
    }
  }

  /// Verifies the OTP for the specified email
  ///
  /// [email] - The email address that received the OTP
  /// [otp] - The 6-digit OTP code to verify
  ///
  /// Returns a Map with success status and message
  ///
  /// Throws an exception if verification fails
  static Future<Map<String, dynamic>> verifyEmailOTP({
    required String email,
    required String otp,
  }) async {
    try {
      final callable = _functions.httpsCallable('verifyOTP');

      final result = await callable.call({
        'email': email.toLowerCase().trim(),
        'otp': otp.trim(),
      });

      if (result.data['success'] == true) {
        return {
          'success': true,
          'message': result.data['message'] ?? 'OTP verified successfully',
        };
      } else {
        throw Exception(result.data['message'] ?? 'Failed to verify OTP');
      }
    } on FirebaseFunctionsException catch (e) {
      // Handle Firebase Functions specific errors
      String errorMessage;

      switch (e.code) {
        case 'invalid-argument':
          errorMessage = 'Invalid OTP format';
          break;
        case 'permission-denied':
          errorMessage = 'Permission denied. Please try again';
          break;
        case 'not-found':
          errorMessage = 'OTP not found. Please request a new one';
          break;
        case 'unavailable':
          errorMessage = 'Service temporarily unavailable. Please try again later';
          break;
        case 'deadline-exceeded':
          errorMessage = 'Request timeout. Please check your internet connection';
          break;
        default:
          errorMessage = e.message ?? 'Failed to verify OTP. Please try again';
      }

      throw Exception(errorMessage);
    } catch (e) {
      // Handle other errors and extract meaningful messages
      String errorMessage = e.toString();

      if (errorMessage.contains('OTP verification failed:')) {
        // Extract the actual error message from Firebase Functions
        errorMessage = errorMessage.replaceFirst('Exception: OTP verification failed: ', '');
      } else if (errorMessage.contains('Exception: ')) {
        errorMessage = errorMessage.replaceFirst('Exception: ', '');
      }

      // Handle specific error cases
      if (errorMessage.contains('OTP has expired')) {
        throw Exception('OTP has expired. Please request a new one');
      } else if (errorMessage.contains('Invalid OTP')) {
        throw Exception('Invalid OTP. Please check and try again');
      } else if (errorMessage.contains('Too many failed attempts')) {
        throw Exception('Too many failed attempts. Please request a new OTP');
      } else if (errorMessage.contains('OTP has already been used')) {
        throw Exception('This OTP has already been used. Please request a new one');
      } else if (errorMessage.contains('No OTP found')) {
        throw Exception('No OTP found for this email. Please request a new one');
      } else {
        throw Exception('Network error. Please check your internet connection and try again');
      }
    }
  }

  /// Gets the current user's email from Firebase Auth
  ///
  /// Returns the email address or null if user is not authenticated
  static String? getCurrentUserEmail() {
    final user = FirebaseAuth.instance.currentUser;
    return user?.email;
  }

  /// Gets the current user's display name from Firebase Auth
  ///
  /// Returns the display name or null if not available
  static String? getCurrentUserName() {
    final user = FirebaseAuth.instance.currentUser;
    return user?.displayName;
  }

  /// Validates email format
  ///
  /// [email] - The email address to validate
  ///
  /// Returns true if email format is valid, false otherwise
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(r'^[^\s@]+@[^\s@]+\.[^\s@]+$');
    return emailRegex.hasMatch(email.trim());
  }

  /// Validates OTP format (6 digits)
  ///
  /// [otp] - The OTP to validate
  ///
  /// Returns true if OTP format is valid, false otherwise
  static bool isValidOTP(String otp) {
    final otpRegex = RegExp(r'^\d{6}$');
    return otpRegex.hasMatch(otp.trim());
  }

  /// Formats time remaining for OTP expiration
  ///
  /// [expiresAt] - ISO string of expiration time
  ///
  /// Returns formatted string like "4:32" or "Expired"
  static String formatTimeRemaining(String? expiresAt) {
    if (expiresAt == null) return 'Unknown';

    try {
      final expiration = DateTime.parse(expiresAt);
      final now = DateTime.now();
      final difference = expiration.difference(now);

      if (difference.isNegative) {
        return 'Expired';
      }

      final minutes = difference.inMinutes;
      final seconds = difference.inSeconds % 60;

      return '$minutes:${seconds.toString().padLeft(2, '0')}';
    } catch (e) {
      return 'Unknown';
    }
  }

  /// Checks if OTP has expired
  ///
  /// [expiresAt] - ISO string of expiration time
  ///
  /// Returns true if expired, false otherwise
  static bool isOTPExpired(String? expiresAt) {
    if (expiresAt == null) return true;

    try {
      final expiration = DateTime.parse(expiresAt);
      final now = DateTime.now();
      return now.isAfter(expiration);
    } catch (e) {
      return true;
    }
  }
}
