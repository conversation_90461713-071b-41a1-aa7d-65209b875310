/**
 * EvenOut Firebase Functions
 * OTP Email Service using Gmail SMTP and NodeMailer
 */

const {setGlobalOptions} = require("firebase-functions");
const {onCall} = require("firebase-functions/v2/https");
const {onRequest} = require("firebase-functions/https");
const {defineSecret} = require("firebase-functions/v2/params");
const logger = require("firebase-functions/logger");
const admin = require("firebase-admin");
const nodemailer = require("nodemailer");
const crypto = require("crypto");

// Initialize Firebase Admin
admin.initializeApp();

// Set global options for cost control
setGlobalOptions({maxInstances: 10});

// Gmail SMTP configuration
const createTransporter = () => {
  // Hardcode the credentials temporarily for testing
  const gmailUser = "<EMAIL>";
  const gmailPassword = "eeasivsixwnyqlhq"; // This should be moved to environment variables

  logger.info("Creating transporter", {
    hasUser: !!gmailUser,
    hasPassword: !!gmailPassword,
    user: gmailUser,
  });

  return nodemailer.createTransport({
    service: "gmail",
    auth: {
      user: gmailUser,
      pass: gmailPassword,
    },
  });
};

// Generate 6-digit OTP
const generateOTP = () => {
  return crypto.randomInt(100000, 999999).toString();
};

// Email template for OTP
const createOTPEmailTemplate = (otp, userName) => {
  return {
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>EvenOut - Email Verification</title>
        <style>
          body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2D5A5A;
            background-color: #F5F1E8;
            margin: 0;
            padding: 0;
          }
          .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-top: 40px;
            margin-bottom: 40px;
          }
          .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #3AD29F, #2D5A5A);
            border-radius: 16px 16px 0 0;
            margin: -20px -20px 30px -20px;
          }
          .logo {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin: 0;
          }
          .subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            margin: 5px 0 0 0;
          }
          .content {
            padding: 0 20px;
          }
          .greeting {
            font-size: 24px;
            font-weight: 600;
            color: #2D5A5A;
            margin-bottom: 20px;
          }
          .message {
            font-size: 16px;
            color: #5A8A8A;
            margin-bottom: 30px;
            line-height: 1.6;
          }
          .otp-container {
            background: linear-gradient(135deg, #F0F8F5, #E8F5F0);
            border: 2px solid #3AD29F;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
          }
          .otp-label {
            font-size: 14px;
            color: #5A8A8A;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 600;
          }
          .otp-code {
            font-size: 36px;
            font-weight: bold;
            color: #2D5A5A;
            letter-spacing: 8px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
          }
          .otp-note {
            font-size: 12px;
            color: #5A8A8A;
            margin-top: 15px;
          }
          .warning {
            background-color: #FFF3E0;
            border-left: 4px solid #E6C068;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
          }
          .warning-text {
            font-size: 14px;
            color: #B8860B;
            margin: 0;
          }
          .footer {
            text-align: center;
            padding: 30px 0;
            border-top: 1px solid #E0E0E0;
            margin-top: 40px;
          }
          .footer-text {
            font-size: 12px;
            color: #5A8A8A;
            margin: 5px 0;
          }
          .app-info {
            background-color: #F8F8F8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
          }
          .app-info-title {
            font-size: 16px;
            font-weight: 600;
            color: #2D5A5A;
            margin-bottom: 10px;
          }
          .app-info-text {
            font-size: 14px;
            color: #5A8A8A;
            margin: 0;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1 class="logo">EvenOut</h1>
            <p class="subtitle">Community Exchange Platform</p>
          </div>

          <div class="content">
            <h2 class="greeting">Hello ${userName || "there"}! 👋</h2>

            <p class="message">
              Welcome to EvenOut! We're excited to have you join our community of people who believe in sharing and exchanging goods sustainably.
            </p>

            <p class="message">
              To complete your email verification, please use the following One-Time Password (OTP):
            </p>

            <div class="otp-container">
              <div class="otp-label">Your Verification Code</div>
              <div class="otp-code">${otp}</div>
              <div class="otp-note">This code expires in 5 minutes</div>
            </div>

            <div class="warning">
              <p class="warning-text">
                <strong>Security Notice:</strong> Never share this code with anyone. EvenOut will never ask for your OTP via phone or email.
              </p>
            </div>

            <div class="app-info">
              <div class="app-info-title">What's Next?</div>
              <p class="app-info-text">
                Once verified, you'll be able to list items, browse your community's offerings, and start making meaningful exchanges with your neighbors.
              </p>
            </div>

            <p class="message">
              If you didn't request this verification, please ignore this email or contact our support team.
            </p>
          </div>

          <div class="footer">
            <p class="footer-text">© 2024 EvenOut - Building Sustainable Communities</p>
            <p class="footer-text">This is an automated message, please do not reply to this email.</p>
            <p class="footer-text">Need help? Contact <NAME_EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
Hello ${userName || "there"}!

Welcome to EvenOut! We're excited to have you join our community.

Your email verification code is: ${otp}

This code expires in 5 minutes.

Security Notice: Never share this code with anyone. EvenOut will never ask for your OTP via phone or email.

If you didn't request this verification, please ignore this email.

© 2024 EvenOut - Building Sustainable Communities
Contact <NAME_EMAIL>
    `,
  };
};

/**
 * Send OTP Email Function
 * Generates and sends a 6-digit OTP to the user's email
 * Stores the OTP in Firestore with 5-minute expiration
 */
exports.sendOTPEmail = onCall(async (request) => {
  try {
    logger.info("sendOTPEmail function called", { data: request.data });

    // Validate input
    const {email, userName} = request.data;

    if (!email) {
      logger.error("Email is required");
      throw new Error("Email is required");
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      logger.error("Invalid email format", { email });
      throw new Error("Invalid email format");
    }

    logger.info("Generating OTP for email", { email, userName });

    // Generate OTP
    const otp = generateOTP();
    const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes from now

    logger.info("OTP generated", { otp, expiresAt });

    // Store OTP in Firestore
    const otpDoc = {
      email: email.toLowerCase(),
      otp: otp,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      expiresAt: expiresAt,
      verified: false,
      attempts: 0,
    };

    await admin.firestore()
        .collection("email_otps")
        .doc(email.toLowerCase())
        .set(otpDoc);

    // Create email template
    const emailTemplate = createOTPEmailTemplate(otp, userName);

    // Configure email options
    const mailOptions = {
      from: {
        name: "EvenOut",
        address: "<EMAIL>",
      },
      to: email,
      subject: `${otp} is your EvenOut verification code`,
      html: emailTemplate.html,
      text: emailTemplate.text,
    };

    // Send email
    logger.info("Creating transporter and sending email");
    const transporter = createTransporter();
    logger.info("Transporter created, sending mail");

    const result = await transporter.sendMail(mailOptions);

    logger.info("OTP email sent successfully", {
      email: email,
      messageId: result.messageId,
      otp: otp, // Remove this in production for security
    });

    return {
      success: true,
      message: "OTP sent successfully",
      expiresAt: expiresAt.toISOString(),
    };
  } catch (error) {
    logger.error("Error sending OTP email", {
      error: error.message,
      stack: error.stack,
      errorType: error.constructor.name,
    });

    throw new Error(`Failed to send OTP: ${error.message}`);
  }
});

/**
 * Verify OTP Function
 * Verifies the OTP provided by the user
 */
exports.verifyOTP = onCall(async (request) => {
  try {
    const {email, otp} = request.data;

    if (!email || !otp) {
      throw new Error("Email and OTP are required");
    }

    // Get OTP document from Firestore
    const otpDocRef = admin.firestore()
        .collection("email_otps")
        .doc(email.toLowerCase());

    const otpDoc = await otpDocRef.get();

    if (!otpDoc.exists) {
      throw new Error("No OTP found for this email");
    }

    const otpData = otpDoc.data();

    // Check if OTP is already verified
    if (otpData.verified) {
      throw new Error("OTP has already been used");
    }

    // Check if OTP is expired
    if (new Date() > otpData.expiresAt.toDate()) {
      throw new Error("OTP has expired");
    }

    // Check attempt limit (max 3 attempts)
    if (otpData.attempts >= 3) {
      throw new Error("Too many failed attempts. Please request a new OTP");
    }

    // Verify OTP
    if (otpData.otp !== otp.toString()) {
      // Increment attempt counter
      await otpDocRef.update({
        attempts: admin.firestore.FieldValue.increment(1),
      });

      throw new Error("Invalid OTP");
    }

    // Mark OTP as verified
    await otpDocRef.update({
      verified: true,
      verifiedAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    logger.info("OTP verified successfully", {
      email: email,
    });

    return {
      success: true,
      message: "OTP verified successfully",
    };
  } catch (error) {
    logger.error("Error verifying OTP", {
      error: error.message,
      email: request.data && request.data.email,
    });

    throw new Error(`OTP verification failed: ${error.message}`);
  }
});

/**
 * Cleanup expired OTPs (runs daily)
 * This function can be triggered by a scheduled job
 */
exports.cleanupExpiredOTPs = onRequest(async (req, res) => {
  try {
    const now = new Date();
    const expiredOTPs = await admin.firestore()
        .collection("email_otps")
        .where("expiresAt", "<", now)
        .get();

    const batch = admin.firestore().batch();
    expiredOTPs.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info(`Cleaned up ${expiredOTPs.size} expired OTPs`);

    res.status(200).json({
      success: true,
      message: `Cleaned up ${expiredOTPs.size} expired OTPs`,
    });
  } catch (error) {
    logger.error("Error cleaning up expired OTPs", {
      error: error.message,
    });

    res.status(500).json({
      success: false,
      error: error.message,
    });
  }
});
